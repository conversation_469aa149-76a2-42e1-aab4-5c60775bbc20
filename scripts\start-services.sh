#!/bin/bash

# B2C Core Service Startup Script
# This script helps start the services in the correct order and provides debugging information

set -e

echo "🚀 Starting B2C Core Service..."
echo "================================"

# Function to check if a service is healthy
check_service_health() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps $service_name | grep -q "healthy"; then
            echo "✅ $service_name is healthy!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to become healthy after $max_attempts attempts"
    return 1
}

# Function to show service logs
show_logs() {
    local service_name=$1
    echo "📋 Recent logs for $service_name:"
    echo "--------------------------------"
    docker-compose logs --tail=20 $service_name
    echo ""
}

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down

# Start database services first
echo "🗄️  Starting database services..."
docker-compose up -d postgres redis

# Wait for database to be healthy
if check_service_health "postgres"; then
    echo "✅ PostgreSQL is ready!"
else
    echo "❌ PostgreSQL failed to start. Showing logs:"
    show_logs "postgres"
    exit 1
fi

# Wait for Redis to be healthy
if check_service_health "redis"; then
    echo "✅ Redis is ready!"
else
    echo "❌ Redis failed to start. Showing logs:"
    show_logs "redis"
    exit 1
fi

# Test database connection
echo "🔍 Testing database connection..."
if docker-compose exec -T postgres psql -U postgres -d db_b2c -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection test passed!"
else
    echo "❌ Database connection test failed!"
    show_logs "postgres"
    exit 1
fi

# Test UUID extension
echo "🔍 Testing UUID extension..."
if docker-compose exec -T postgres psql -U postgres -d db_b2c -c "SELECT uuid_generate_v4();" > /dev/null 2>&1; then
    echo "✅ UUID extension test passed!"
else
    echo "❌ UUID extension test failed!"
    show_logs "postgres"
    exit 1
fi

# Start the main application
echo "🚀 Starting B2C Core Service..."
docker-compose up -d b2c-api

# Wait for the application to be healthy
if check_service_health "b2c-api"; then
    echo "✅ B2C Core Service is healthy!"
else
    echo "❌ B2C Core Service failed to start. Showing logs:"
    show_logs "b2c-api"
    exit 1
fi

# Start the gateway
echo "🌐 Starting API Gateway..."
docker-compose up -d gateway

echo ""
echo "🎉 All services started successfully!"
echo "================================"
echo "📊 Service Status:"
docker-compose ps
echo ""
echo "🔗 Available endpoints:"
echo "   - API: http://localhost:8080"
echo "   - Gateway: http://localhost:80"
echo "   - Health Check: http://localhost:8080/health"
echo ""
echo "📋 To view logs: docker-compose logs -f [service-name]"
echo "🛑 To stop all services: docker-compose down"
