package registry

import (
	"b2c-core-service/internal/auth"
	"b2c-core-service/internal/user"
	// Future domains can be added here:
	// "b2c-core-service/internal/product"
	// "b2c-core-service/internal/cart"
	// "b2c-core-service/internal/order"
	// "b2c-core-service/internal/shop"
	// "b2c-core-service/internal/category"
)

// RegisterAllDomains registers all domain registries
// This function should be called during application initialization
func RegisterAllDomains() {
	// Register auth domain
	auth.RegisterWithRegistry(func(registry interface{}) {
		Register(registry.(HttpRegistry))
	})

	// Register user domain
	user.RegisterWithRegistry(func(registry interface{}) {
		Register(registry.(HttpRegistry))
	})

	// Future domains can be registered here:
	// product.RegisterWithRegistry(func(registry interface{}) {
	//     Register(registry.(HttpRegistry))
	// })
}
