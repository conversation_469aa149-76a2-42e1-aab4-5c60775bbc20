#!/bin/bash

# B2C Core Service Troubleshooting Script
# This script helps diagnose common issues

echo "🔍 B2C Core Service Troubleshooting"
echo "==================================="

# Function to run a command and show result
run_check() {
    local description=$1
    local command=$2
    
    echo ""
    echo "🔍 $description"
    echo "---"
    
    if eval $command; then
        echo "✅ PASS"
    else
        echo "❌ FAIL"
    fi
}

# Check Docker
run_check "Docker is running" "docker --version"

# Check Docker Compose
run_check "Docker Compose is available" "docker-compose --version"

# Check if containers exist
echo ""
echo "📦 Container Status:"
echo "-------------------"
docker-compose ps

# Check container logs
echo ""
echo "📋 Recent Container Logs:"
echo "------------------------"

services=("postgres" "redis" "b2c-api")
for service in "${services[@]}"; do
    echo ""
    echo "🔍 $service logs (last 10 lines):"
    docker-compose logs --tail=10 $service 2>/dev/null || echo "   Service not running or logs unavailable"
done

# Check network connectivity
echo ""
echo "🌐 Network Connectivity:"
echo "-----------------------"

# Check if postgres container can be reached
if docker-compose exec -T b2c-api ping -c 1 postgres > /dev/null 2>&1; then
    echo "✅ b2c-api can reach postgres"
else
    echo "❌ b2c-api cannot reach postgres"
fi

# Check if redis container can be reached
if docker-compose exec -T b2c-api ping -c 1 redis > /dev/null 2>&1; then
    echo "✅ b2c-api can reach redis"
else
    echo "❌ b2c-api cannot reach redis"
fi

# Database specific checks
echo ""
echo "🗄️  Database Checks:"
echo "-------------------"

# Check if PostgreSQL is accepting connections
if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo "✅ PostgreSQL is accepting connections"
else
    echo "❌ PostgreSQL is not accepting connections"
fi

# Check if database exists
if docker-compose exec -T postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw db_b2c; then
    echo "✅ Database 'db_b2c' exists"
else
    echo "❌ Database 'db_b2c' does not exist"
fi

# Check UUID extension
if docker-compose exec -T postgres psql -U postgres -d db_b2c -c "SELECT uuid_generate_v4();" > /dev/null 2>&1; then
    echo "✅ UUID extension is working"
else
    echo "❌ UUID extension is not working"
fi

# Check schema
if docker-compose exec -T postgres psql -U postgres -d db_b2c -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'cashandcarry';" | grep -q cashandcarry; then
    echo "✅ Schema 'cashandcarry' exists"
else
    echo "❌ Schema 'cashandcarry' does not exist"
fi

# Application specific checks
echo ""
echo "🚀 Application Checks:"
echo "---------------------"

# Check if health endpoint is responding
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Health endpoint is responding"
    echo "   Response: $(curl -s http://localhost:8080/health)"
else
    echo "❌ Health endpoint is not responding"
fi

# Check environment variables
echo ""
echo "🔧 Environment Variables (in b2c-api container):"
echo "-----------------------------------------------"
docker-compose exec -T b2c-api env | grep -E "^(DB_|REDIS_|JWT_|SERVER_)" | sort || echo "   Container not running"

echo ""
echo "💡 Common Solutions:"
echo "==================="
echo "1. If PostgreSQL connection fails:"
echo "   - Run: docker-compose down && docker-compose up -d postgres"
echo "   - Wait for health check to pass"
echo ""
echo "2. If UUID extension fails:"
echo "   - Check PostgreSQL logs: docker-compose logs postgres"
echo "   - Recreate database: docker-compose down -v && docker-compose up -d"
echo ""
echo "3. If application won't start:"
echo "   - Check application logs: docker-compose logs b2c-api"
echo "   - Verify environment variables are set correctly"
echo ""
echo "4. If network issues:"
echo "   - Restart all services: docker-compose restart"
echo "   - Check Docker network: docker network ls"
echo ""
echo "🔄 To restart everything: docker-compose down && docker-compose up -d"
