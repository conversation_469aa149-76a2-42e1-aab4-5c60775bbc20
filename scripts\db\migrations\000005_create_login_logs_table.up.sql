-- Create login_logs table
CREATE TABLE cashandcarry.login_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES cashandcarry.users(id) ON DELETE CASCADE,
    ip INET NOT NULL,
    device TEXT,
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_login_logs_user_id ON cashandcarry.login_logs(user_id);
CREATE INDEX idx_login_logs_timestamp ON cashandcarry.login_logs(timestamp);
CREATE INDEX idx_login_logs_ip ON cashandcarry.login_logs(ip);

-- Add comments
COMMENT ON TABLE cashandcarry.login_logs IS 'Audit trail for user login activities';
COMMENT ON COLUMN cashandcarry.login_logs.user_id IS 'Reference to the user account';
COMMENT ON COLUMN cashandcarry.login_logs.ip IS 'IP address of the login attempt';
COMMENT ON COLUMN cashandcarry.login_logs.device IS 'Device information (user agent, etc.)';
COMMENT ON COLUMN cashandcarry.login_logs.timestamp IS 'When the login occurred';
