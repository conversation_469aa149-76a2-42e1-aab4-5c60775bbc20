-- Rollback initialization of B2C Core Service database

-- Drop helper functions
DROP FUNCTION IF EXISTS cashandcarry.update_updated_at_column();
DROP FUNCTION IF EXISTS cashandcarry.set_timestamps();

-- Reset search path
ALTER DATABASE db_b2c RESET search_path;

-- Revoke permissions
R<PERSON><PERSON><PERSON> ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA cashandcarry FROM postgres;
REVOKE ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cashandcarry FROM postgres;
REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA cashandcarry FROM postgres;
REVOKE ALL PRIVILEGES ON SCHEMA cashandcarry FROM postgres;

-- Drop schema (this will fail if there are objects in it)
DROP SCHEMA IF EXISTS cashandcarry CASCADE;

-- Drop UUID extension (be careful - other databases might use it)
-- DROP EXTENSION IF EXISTS "uuid-ossp";
