version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: b2c-postgres
    environment:
      POSTGRES_DB: db_b2c
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      # Ensure proper logging
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - b2c-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d db_b2c"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 30s
    restart: unless-stopped

  # Redis (for future use)
  redis:
    image: redis:7-alpine
    container_name: b2c-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - b2c-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s
    restart: unless-stopped

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: b2c-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - b2c-network
    depends_on:
      - postgres

  # B2C Core Service
  b2c-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: b2c-core-service
    environment:
      - SERVER_PORT=8080
      - ENVIRONMENT=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=db_b2c
      - DB_SCHEMA=cashandcarry
      - DB_SSLMODE=disable
      - JWT_ACCESS_SECRET=dev-access-secret-key
      - JWT_REFRESH_SECRET=dev-refresh-secret-key
      - JWT_ACCESS_EXPIRY_HRS=1
      - JWT_REFRESH_EXPIRY_HRS=168
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "8080:8080"
    networks:
      - b2c-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # OpenResty (NGINX with Lua) - API Gateway
  gateway:
    image: openresty/openresty:alpine
    container_name: b2c-gateway
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - b2c-network
    depends_on:
      - b2c-api
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  b2c-network:
    driver: bridge
