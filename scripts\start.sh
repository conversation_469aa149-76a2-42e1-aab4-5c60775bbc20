#!/bin/sh

# Startup script for B2C Core Service
# This script runs database migrations before starting the application

set -e

echo "🚀 Starting B2C Core Service..."
echo "================================"

# Configuration from environment variables
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-postgres}
DB_NAME=${DB_NAME:-db_b2c}
MIGRATIONS_DIR="/root/migrations"

# Build database URL
DATABASE_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable&search_path=cashandcarry,public"

echo "📊 Database Configuration:"
echo "  Host: $DB_HOST:$DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""

# Function to wait for database to be ready
wait_for_database() {
    echo "⏳ Waiting for database to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER > /dev/null 2>&1; then
            echo "✅ Database is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - Database not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Database failed to become ready after $max_attempts attempts"
    exit 1
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."
    
    if [ ! -d "$MIGRATIONS_DIR" ]; then
        echo "❌ Migrations directory not found: $MIGRATIONS_DIR"
        exit 1
    fi
    
    # Check if migrate tool exists
    if ! command -v migrate > /dev/null 2>&1; then
        echo "❌ migrate tool not found in container"
        exit 1
    fi
    
    # Run migrations
    if migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" up; then
        echo "✅ Database migrations completed successfully!"
    else
        echo "❌ Database migrations failed!"
        exit 1
    fi
}

# Function to verify database setup
verify_database() {
    echo "🔍 Verifying database setup..."
    
    # Test basic connection
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "✅ Database connection verified"
    else
        echo "❌ Database connection failed"
        exit 1
    fi
    
    # Test UUID extension
    if psql "$DATABASE_URL" -c "SELECT uuid_generate_v4();" > /dev/null 2>&1; then
        echo "✅ UUID extension verified"
    else
        echo "❌ UUID extension not working"
        exit 1
    fi
    
    # Check if main tables exist
    if psql "$DATABASE_URL" -c "SELECT 1 FROM cashandcarry.users LIMIT 1;" > /dev/null 2>&1; then
        echo "✅ Database schema verified"
    else
        echo "❌ Database schema verification failed"
        exit 1
    fi
}

# Main startup sequence
main() {
    echo "🔧 Starting database setup..."
    
    # Wait for database to be available
    wait_for_database
    
    # Run migrations
    run_migrations
    
    # Verify everything is working
    verify_database
    
    echo ""
    echo "🎉 Database setup completed successfully!"
    echo "🚀 Starting application..."
    echo ""
    
    # Start the main application
    exec ./main
}

# Run main function
main
