#!/bin/sh

# Startup script for B2C Core Service
# This script runs database migrations before starting the application

set -e

echo "🚀 Starting B2C Core Service..."
echo "================================"

# Configuration from environment variables
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-postgres}
DB_NAME=${DB_NAME:-db_b2c}
MIGRATIONS_DIR="/root/migrations"

# Build database URL
DATABASE_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable&search_path=cashandcarry,public"

echo "📊 Database Configuration:"
echo "  Host: $DB_HOST:$DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""

# Function to wait for database to be ready
wait_for_database() {
    echo "⏳ Waiting for database to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER > /dev/null 2>&1; then
            echo "✅ Database is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - Database not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Database failed to become ready after $max_attempts attempts"
    exit 1
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."

    if [ ! -d "$MIGRATIONS_DIR" ]; then
        echo "❌ Migrations directory not found: $MIGRATIONS_DIR"
        exit 1
    fi

    # Check if migrate tool exists
    if ! command -v migrate > /dev/null 2>&1; then
        echo "❌ migrate tool not found in container"
        exit 1
    fi

    # Check current migration status
    echo "📊 Checking migration status..."
    migration_output=$(migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" version 2>&1)
    migration_exit_code=$?

    echo "Migration status output: $migration_output"

    # Handle dirty database state
    if echo "$migration_output" | grep -q "(dirty)"; then
        echo "⚠️  Database is in dirty state. Attempting to fix..."

        # Extract version number from dirty state message (format: "2 (dirty)")
        dirty_version=$(echo "$migration_output" | grep -o "[0-9]* (dirty)" | grep -o "[0-9]*")

        if [ -n "$dirty_version" ]; then
            echo "🔧 Forcing migration to version $dirty_version..."
            if migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" force $dirty_version; then
                echo "✅ Successfully forced migration to version $dirty_version"
            else
                echo "❌ Failed to force migration version"
                exit 1
            fi
        else
            echo "❌ Could not extract version from dirty state"
            echo "Raw output: $migration_output"
            exit 1
        fi
    fi

    # Run migrations
    echo "🚀 Running migrations up..."
    migration_up_output=$(migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" up 2>&1)
    migration_up_exit_code=$?

    echo "Migration up output: $migration_up_output"

    if [ $migration_up_exit_code -eq 0 ]; then
        echo "✅ Database migrations completed successfully!"
    else
        echo "❌ Database migrations failed!"
        echo "Error details: $migration_up_output"
        exit 1
    fi
}

# Function to verify database setup
verify_database() {
    echo "🔍 Verifying database setup..."

    # Since migrations completed successfully, we know the database is working
    # Let's do a simple verification using the migrate tool itself
    echo "Testing migration status..."
    if migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" version > /dev/null 2>&1; then
        echo "✅ Database connection verified via migrate tool"
    else
        echo "❌ Database verification failed"
        exit 1
    fi

    echo "✅ Database setup verification completed!"
}

# Main startup sequence
main() {
    echo "🔧 Starting database setup..."
    
    # Wait for database to be available
    wait_for_database
    
    # Run migrations
    run_migrations
    
    # Verify everything is working
    verify_database
    
    echo ""
    echo "🎉 Database setup completed successfully!"
    echo "🚀 Starting application..."
    echo ""
    
    # Start the main application
    exec ./main
}

# Run main function
main
