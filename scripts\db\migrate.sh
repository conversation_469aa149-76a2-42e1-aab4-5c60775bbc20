#!/bin/bash

# Database Migration Script for B2C Core Service
# This script uses golang-migrate to manage database migrations

set -e

# Configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-postgres}
DB_NAME=${DB_NAME:-db_b2c}
MIGRATIONS_DIR="scripts/db/migrations"

# Build database URL
DATABASE_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable&search_path=cashandcarry,public"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if migrate tool is installed
check_migrate_tool() {
    if ! command -v migrate &> /dev/null; then
        print_error "golang-migrate tool is not installed"
        echo "Install it using one of the following methods:"
        echo ""
        echo "Using Go:"
        echo "  go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest"
        echo ""
        echo "Using curl (Linux/macOS):"
        echo "  curl -L https://github.com/golang-migrate/migrate/releases/download/v4.16.2/migrate.linux-amd64.tar.gz | tar xvz"
        echo "  sudo mv migrate /usr/local/bin/"
        echo ""
        echo "Using Homebrew (macOS):"
        echo "  brew install golang-migrate"
        echo ""
        exit 1
    fi
}

# Function to wait for database to be ready
wait_for_db() {
    print_status "Waiting for database to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER > /dev/null 2>&1; then
            print_success "Database is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - Database not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Database failed to become ready after $max_attempts attempts"
    exit 1
}

# Function to run migrations up
migrate_up() {
    print_status "Running migrations up..."
    migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" up
    print_success "Migrations completed successfully!"
}

# Function to run migrations down
migrate_down() {
    local steps=${1:-1}
    print_warning "Running $steps migration(s) down..."
    print_warning "This will DESTROY data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" down $steps
        print_success "Rollback completed!"
    else
        print_status "Rollback cancelled."
    fi
}

# Function to show migration status
migrate_status() {
    print_status "Migration status:"
    migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" version
}

# Function to force migration version
migrate_force() {
    local version=$1
    if [ -z "$version" ]; then
        print_error "Please specify a version to force"
        exit 1
    fi
    
    print_warning "Forcing migration to version $version..."
    print_warning "This should only be used to fix broken migration state. Continue? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        migrate -path $MIGRATIONS_DIR -database "$DATABASE_URL" force $version
        print_success "Migration version forced to $version"
    else
        print_status "Force operation cancelled."
    fi
}

# Function to create a new migration
migrate_create() {
    local name=$1
    if [ -z "$name" ]; then
        print_error "Please specify a migration name"
        echo "Usage: $0 create <migration_name>"
        exit 1
    fi
    
    print_status "Creating new migration: $name"
    migrate create -ext sql -dir $MIGRATIONS_DIR -seq $name
    print_success "Migration files created!"
}

# Function to show help
show_help() {
    echo "Database Migration Tool for B2C Core Service"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  up              Run all pending migrations"
    echo "  down [steps]    Rollback migrations (default: 1 step)"
    echo "  status          Show current migration status"
    echo "  force <version> Force migration to specific version"
    echo "  create <name>   Create new migration files"
    echo "  help            Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DB_HOST         Database host (default: localhost)"
    echo "  DB_PORT         Database port (default: 5432)"
    echo "  DB_USER         Database user (default: postgres)"
    echo "  DB_PASSWORD     Database password (default: postgres)"
    echo "  DB_NAME         Database name (default: db_b2c)"
    echo ""
    echo "Examples:"
    echo "  $0 up                    # Run all pending migrations"
    echo "  $0 down 2                # Rollback 2 migrations"
    echo "  $0 create add_user_table # Create new migration"
    echo "  $0 status                # Show migration status"
}

# Main script logic
main() {
    local command=$1
    
    case $command in
        "up")
            check_migrate_tool
            wait_for_db
            migrate_up
            ;;
        "down")
            check_migrate_tool
            wait_for_db
            migrate_down $2
            ;;
        "status")
            check_migrate_tool
            wait_for_db
            migrate_status
            ;;
        "force")
            check_migrate_tool
            wait_for_db
            migrate_force $2
            ;;
        "create")
            check_migrate_tool
            migrate_create $2
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
