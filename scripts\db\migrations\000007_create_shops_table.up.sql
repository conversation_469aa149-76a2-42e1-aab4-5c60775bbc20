-- Create shops table
CREATE TABLE cashandcarry.shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES cashandcarry.shop_categories(id) ON DELETE RESTRICT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    pincode VARCHAR(10) NOT NULL,
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- <PERSON>reate indexes
CREATE INDEX idx_shops_category_id ON cashandcarry.shops(category_id);
CREATE INDEX idx_shops_pincode ON cashandcarry.shops(pincode);
CREATE INDEX idx_shops_is_active ON cashandcarry.shops(is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_shops_rating ON cashandcarry.shops(rating) WHERE deleted_at IS NULL;
CREATE INDEX idx_shops_deleted_at ON cashandcarry.shops(deleted_at);
CREATE INDEX idx_shops_name_search ON cashandcarry.shops USING gin(to_tsvector('english', name)) WHERE deleted_at IS NULL;

-- Add trigger to automatically update updated_at
CREATE TRIGGER update_shops_updated_at
    BEFORE UPDATE ON cashandcarry.shops
    FOR EACH ROW
    EXECUTE FUNCTION cashandcarry.update_updated_at_column();

-- Add comments
COMMENT ON TABLE cashandcarry.shops IS 'Shops/stores in the platform';
COMMENT ON COLUMN cashandcarry.shops.category_id IS 'Reference to shop category';
COMMENT ON COLUMN cashandcarry.shops.name IS 'Shop name';
COMMENT ON COLUMN cashandcarry.shops.description IS 'Shop description';
COMMENT ON COLUMN cashandcarry.shops.image_url IS 'Shop image/logo URL';
COMMENT ON COLUMN cashandcarry.shops.pincode IS 'Shop location pincode';
COMMENT ON COLUMN cashandcarry.shops.rating IS 'Average shop rating (0-5)';
COMMENT ON COLUMN cashandcarry.shops.is_active IS 'Whether the shop is currently active';
COMMENT ON COLUMN cashandcarry.shops.deleted_at IS 'Soft delete timestamp';
