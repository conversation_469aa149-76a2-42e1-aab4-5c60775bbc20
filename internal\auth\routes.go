package auth

import (
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Registry implements the HttpRegistry interface for auth domain
type Registry struct {
	handler *<PERSON><PERSON>
}

// NewRegistry creates a new auth registry
func NewRegistry() *Registry {
	return &Registry{}
}

// GetPrefix returns the URL prefix for auth routes
func (r *Registry) GetPrefix() string {
	return "/auth"
}

// Initialize sets up the auth domain dependencies
func (r *Registry) Initialize(db *gorm.DB, jwtService *auth.JWTService) error {
	// Initialize repository
	repo := NewRepository(db)

	// Initialize service
	service := NewService(repo, jwtService)

	// Initialize handler
	r.handler = NewHandler(service)

	return nil
}

// RegisterRoutes registers auth routes with the router
func (r *Registry) RegisterRoutes(router *gin.RouterGroup, jwtService *auth.JWTService) {
	authGroup := router.Group(r.GetPrefix())
	{
		// Public routes
		authGroup.POST("/register", r.handler.Register)
		authGroup.POST("/login", r.handler.Login)
		authGroup.POST("/refresh", r.handler.RefreshToken)

		// Protected routes
		protected := authGroup.Group("")
		protected.Use(middlewares.AuthMiddleware(jwtService))
		{
			protected.GET("/profile", r.handler.GetProfile)
			protected.POST("/verify", r.handler.VerifyUser)
		}
	}
}

// SetupRoutes sets up auth routes (legacy function for backward compatibility)
// Deprecated: Use Registry instead
func SetupRoutes(router *gin.RouterGroup, handler *Handler, jwtService *auth.JWTService) {
	authGroup := router.Group("/auth")
	{
		// Public routes
		authGroup.POST("/register", handler.Register)
		authGroup.POST("/login", handler.Login)
		authGroup.POST("/refresh", handler.RefreshToken)

		// Protected routes
		protected := authGroup.Group("")
		protected.Use(middlewares.AuthMiddleware(jwtService))
		{
			protected.GET("/profile", handler.GetProfile)
			protected.POST("/verify", handler.VerifyUser)
		}
	}
}
