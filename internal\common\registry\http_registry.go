package registry

import (
	"b2c-core-service/internal/common/auth"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HttpRegistry defines the interface that all domain routers must implement
type HttpRegistry interface {
	// RegisterRoutes registers the domain routes with the given router group
	RegisterRoutes(router *gin.RouterGroup, jwtService *auth.JWTService)
	
	// GetPrefix returns the URL prefix for this domain (e.g., "/auth", "/user")
	GetPrefix() string
	
	// Initialize performs any initialization required for the domain
	// This includes setting up repositories, services, and handlers
	Initialize(db *gorm.DB, jwtService *auth.JWTService) error
}

// registries holds all registered HTTP registries
var registries []HttpRegistry

// Register adds a new HTTP registry to the global registry list
func Register(registry HttpRegistry) {
	registries = append(registries, registry)
}

// InitializeAll initializes all registered HTTP registries
func InitializeAll(db *gorm.DB, jwtService *auth.JWTService) error {
	for _, registry := range registries {
		if err := registry.Initialize(db, jwtService); err != nil {
			return err
		}
	}
	return nil
}

// RegisterAllRoutes registers routes for all registered HTTP registries
func RegisterAllRoutes(router *gin.RouterGroup, jwtService *auth.JWTService) {
	for _, registry := range registries {
		registry.RegisterRoutes(router, jwtService)
	}
}

// GetRegisteredCount returns the number of registered HTTP registries
func GetRegisteredCount() int {
	return len(registries)
}

// GetRegisteredPrefixes returns all registered route prefixes
func GetRegisteredPrefixes() []string {
	prefixes := make([]string, len(registries))
	for i, registry := range registries {
		prefixes[i] = registry.GetPrefix()
	}
	return prefixes
}
