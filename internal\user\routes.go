package user

import (
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Registry implements the HttpRegistry interface for user domain
type Registry struct {
	handler *<PERSON><PERSON>
}

// NewRegistry creates a new user registry
func NewRegistry() *Registry {
	return &Registry{}
}

// GetPrefix returns the URL prefix for user routes
func (r *Registry) GetPrefix() string {
	return "/user"
}

// Initialize sets up the user domain dependencies
func (r *Registry) Initialize(db *gorm.DB, jwtService *auth.JWTService) error {
	// Initialize repository
	repo := NewRepository(db)

	// Initialize service
	service := NewService(repo)

	// Initialize handler
	r.handler = NewHandler(service)

	return nil
}

// RegisterRoutes registers user routes with the router
func (r *Registry) RegisterRoutes(router *gin.RouterGroup, jwtService *auth.JWTService) {
	userGroup := router.Group(r.GetPrefix())
	userGroup.Use(middlewares.AuthMiddleware(jwtService))
	{
		// Profile routes
		userGroup.POST("/profile", r.handler.CreateProfile)
		userGroup.GET("/profile", r.handler.GetProfile)
		userGroup.PUT("/profile", r.handler.UpdateProfile)
		userGroup.DELETE("/profile", r.handler.DeleteProfile)

		// Address routes
		userGroup.POST("/addresses", r.handler.CreateAddress)
		userGroup.GET("/addresses", r.handler.GetAddresses)
		userGroup.GET("/addresses/:id", r.handler.GetAddress)
		userGroup.PUT("/addresses/:id", r.handler.UpdateAddress)
		userGroup.DELETE("/addresses/:id", r.handler.DeleteAddress)
		userGroup.PATCH("/addresses/:id/default", r.handler.SetDefaultAddress)
	}
}

// SetupRoutes sets up user routes (legacy function for backward compatibility)
// Deprecated: Use Registry instead
func SetupRoutes(router *gin.RouterGroup, handler *Handler, jwtService *auth.JWTService) {
	userGroup := router.Group("/user")
	userGroup.Use(middlewares.AuthMiddleware(jwtService))
	{
		// Profile routes
		userGroup.POST("/profile", handler.CreateProfile)
		userGroup.GET("/profile", handler.GetProfile)
		userGroup.PUT("/profile", handler.UpdateProfile)
		userGroup.DELETE("/profile", handler.DeleteProfile)

		// Address routes
		userGroup.POST("/addresses", handler.CreateAddress)
		userGroup.GET("/addresses", handler.GetAddresses)
		userGroup.GET("/addresses/:id", handler.GetAddress)
		userGroup.PUT("/addresses/:id", handler.UpdateAddress)
		userGroup.DELETE("/addresses/:id", handler.DeleteAddress)
		userGroup.PATCH("/addresses/:id/default", handler.SetDefaultAddress)
	}
}
