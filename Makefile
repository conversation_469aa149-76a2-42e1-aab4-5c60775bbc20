# B2C Core Service Makefile

# Variables
APP_NAME := b2c-core-service
DOCKER_IMAGE := $(APP_NAME):latest
DOCKER_COMPOSE_FILE := docker-compose.yml
GO_FILES := $(shell find . -name "*.go" -type f)

# Default target
.PHONY: help
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Development commands
.PHONY: setup
setup: ## Setup development environment
	@echo "Setting up development environment..."
	cp .env.example .env
	go mod download
	go mod tidy
	@echo "Setup complete! Edit .env file with your configuration."

.PHONY: run
run: ## Run the application locally
	@echo "Starting application..."
	go run cmd/main.go

.PHONY: build
build: ## Build the application binary
	@echo "Building application..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(APP_NAME) cmd/main.go

.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	go clean

# Testing commands
.PHONY: test
test: ## Run tests
	@echo "Running tests..."
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: lint
lint: ## Run linter
	@echo "Running linter..."
	golangci-lint run

# Database commands
.PHONY: db-up
db-up: ## Start database services
	@echo "Starting database services..."
	docker-compose up -d postgres redis

.PHONY: db-down
db-down: ## Stop database services
	@echo "Stopping database services..."
	docker-compose down postgres redis

.PHONY: db-reset
db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "Resetting database..."
	docker-compose down -v postgres
	docker-compose up -d postgres
	@echo "Waiting for database to be ready..."
	sleep 15

.PHONY: db-test
db-test: ## Test database connection and UUID extension
	@echo "Testing database connection..."
	docker-compose exec postgres psql -U postgres -d db_b2c -c "SELECT 1;"
	@echo "Testing UUID extension..."
	docker-compose exec postgres psql -U postgres -d db_b2c -c "SELECT uuid_generate_v4();"

# Migration commands
.PHONY: migrate-up
migrate-up: ## Run database migrations up
	@echo "Running database migrations..."
	chmod +x scripts/db/migrate.sh
	./scripts/db/migrate.sh up

.PHONY: migrate-down
migrate-down: ## Rollback database migrations (specify STEPS=n for multiple)
	@echo "Rolling back database migrations..."
	chmod +x scripts/db/migrate.sh
	./scripts/db/migrate.sh down $(STEPS)

.PHONY: migrate-status
migrate-status: ## Show migration status
	@echo "Checking migration status..."
	chmod +x scripts/db/migrate.sh
	./scripts/db/migrate.sh status

.PHONY: migrate-create
migrate-create: ## Create new migration (specify NAME=migration_name)
	@echo "Creating new migration..."
	chmod +x scripts/db/migrate.sh
	./scripts/db/migrate.sh create $(NAME)

.PHONY: migrate-force
migrate-force: ## Force migration to specific version (specify VERSION=n)
	@echo "Forcing migration version..."
	chmod +x scripts/db/migrate.sh
	./scripts/db/migrate.sh force $(VERSION)

# Docker commands
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

.PHONY: docker-run
docker-run: ## Run application in Docker
	@echo "Running application in Docker..."
	docker run --rm -p 8080:8080 --env-file .env $(DOCKER_IMAGE)

.PHONY: docker-up
docker-up: ## Start all services with Docker Compose
	@echo "Starting all services..."
	docker-compose up -d

.PHONY: docker-down
docker-down: ## Stop all services
	@echo "Stopping all services..."
	docker-compose down

.PHONY: docker-logs
docker-logs: ## Show logs from all services
	docker-compose logs -f

.PHONY: docker-restart
docker-restart: ## Restart all services
	@echo "Restarting all services..."
	docker-compose restart

.PHONY: start-services
start-services: ## Start services with proper order and health checks
	@echo "Starting services with health checks..."
	chmod +x scripts/start-services.sh
	./scripts/start-services.sh

.PHONY: troubleshoot
troubleshoot: ## Run troubleshooting diagnostics
	@echo "Running troubleshooting diagnostics..."
	chmod +x scripts/troubleshoot.sh
	./scripts/troubleshoot.sh

# API Documentation
.PHONY: swag-init
swag-init: ## Generate Swagger documentation
	@echo "Generating Swagger documentation..."
	swag init -g cmd/main.go -o docs/

.PHONY: swag-fmt
swag-fmt: ## Format Swagger comments
	@echo "Formatting Swagger comments..."
	swag fmt

# Development workflow
.PHONY: dev
dev: db-up ## Start development environment
	@echo "Starting development environment..."
	@echo "Database services are starting..."
	sleep 10
	@echo "Starting application..."
	go run cmd/main.go

.PHONY: dev-reset
dev-reset: docker-down docker-up ## Reset development environment
	@echo "Development environment reset complete"

# Production commands
.PHONY: prod-build
prod-build: ## Build for production
	@echo "Building for production..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o bin/$(APP_NAME) cmd/main.go

.PHONY: prod-docker
prod-docker: ## Build production Docker image
	@echo "Building production Docker image..."
	docker build -t $(APP_NAME):prod -f Dockerfile.prod .

# Utility commands
.PHONY: fmt
fmt: ## Format Go code
	@echo "Formatting Go code..."
	go fmt ./...

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	go vet ./...

.PHONY: mod-tidy
mod-tidy: ## Tidy Go modules
	@echo "Tidying Go modules..."
	go mod tidy

.PHONY: deps
deps: ## Download dependencies
	@echo "Downloading dependencies..."
	go mod download

# Health check
.PHONY: health
health: ## Check application health
	@echo "Checking application health..."
	curl -f http://localhost:8080/health || echo "Application is not running"

# Install tools
.PHONY: install-tools
install-tools: ## Install development tools
	@echo "Installing development tools..."
	go install github.com/swaggo/swag/cmd/swag@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "Tools installed successfully"

# Quick start
.PHONY: quick-start
quick-start: setup install-tools swag-init docker-up ## Quick start for new developers
	@echo "Quick start complete!"
	@echo "Application will be available at: http://localhost:8080"
	@echo "API Documentation: http://localhost:8080/docs/index.html"
	@echo "pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
