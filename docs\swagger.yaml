basePath: /api/v1
definitions:
  domain.APIResponse:
    properties:
      data: {}
      error:
        type: string
      message:
        type: string
      success:
        type: boolean
    type: object
  domain.AddressDTO:
    properties:
      city:
        type: string
      id:
        type: string
      is_default:
        type: boolean
      line1:
        type: string
      line2:
        type: string
      location:
        type: string
      pincode:
        type: string
      state:
        type: string
      type:
        type: string
      user_id:
        type: string
    type: object
  domain.CreateAddressRequest:
    properties:
      city:
        type: string
      is_default:
        type: boolean
      line1:
        type: string
      line2:
        type: string
      location:
        type: string
      pincode:
        type: string
      state:
        type: string
      type:
        type: string
    required:
    - city
    - line1
    - pincode
    - state
    - type
    type: object
  domain.CreateProfileRequest:
    properties:
      dob:
        type: string
      first_name:
        type: string
      gender:
        type: string
      language:
        type: string
      last_name:
        type: string
    required:
    - first_name
    - last_name
    type: object
  domain.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  domain.LoginResponse:
    properties:
      access_token:
        type: string
      expires_at:
        type: string
      refresh_token:
        type: string
      user:
        $ref: '#/definitions/domain.UserDTO'
    type: object
  domain.ProfileDTO:
    properties:
      dob:
        type: string
      first_name:
        type: string
      gender:
        type: string
      id:
        type: string
      language:
        type: string
      last_name:
        type: string
      user_id:
        type: string
    type: object
  domain.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  domain.RegisterRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      phone:
        type: string
    required:
    - email
    - password
    - phone
    type: object
  domain.UserDTO:
    properties:
      app_coins:
        type: integer
      created_at:
        type: string
      email:
        type: string
      id:
        type: string
      is_verified:
        type: boolean
      phone:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: A production-grade API service for grocery e-commerce platform
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: B2C Core Service API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user and return tokens
      parameters:
      - description: Login request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.LoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
      summary: Login user
      tags:
      - auth
  /auth/profile:
    get:
      description: Get current user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.UserDTO'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - auth
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Generate new access token using refresh token
      parameters:
      - description: Refresh token request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.LoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
      summary: Refresh access token
      tags:
      - auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: Create a new user account
      parameters:
      - description: Registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.LoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/domain.APIResponse'
      summary: Register a new user
      tags:
      - auth
  /auth/verify:
    post:
      description: Mark user account as verified
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Verify user account
      tags:
      - auth
  /user/addresses:
    get:
      description: Get all addresses of the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.AddressDTO'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user addresses
      tags:
      - user
    post:
      consumes:
      - application/json
      description: Create a new address for the authenticated user
      parameters:
      - description: Address creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateAddressRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.AddressDTO'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Create user address
      tags:
      - user
  /user/addresses/{id}:
    delete:
      description: Delete a specific address of the authenticated user
      parameters:
      - description: Address ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete user address
      tags:
      - user
    get:
      description: Get a specific address of the authenticated user
      parameters:
      - description: Address ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.AddressDTO'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user address
      tags:
      - user
    put:
      consumes:
      - application/json
      description: Update a specific address of the authenticated user
      parameters:
      - description: Address ID
        in: path
        name: id
        required: true
        type: string
      - description: Address update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateAddressRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.AddressDTO'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Update user address
      tags:
      - user
  /user/addresses/{id}/default:
    patch:
      description: Set a specific address as the default address for the authenticated
        user
      parameters:
      - description: Address ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Set default address
      tags:
      - user
  /user/profile:
    delete:
      description: Delete the profile of the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete user profile
      tags:
      - user
    get:
      description: Get the profile of the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ProfileDTO'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - user
    post:
      consumes:
      - application/json
      description: Create a new profile for the authenticated user
      parameters:
      - description: Profile creation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateProfileRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ProfileDTO'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Create user profile
      tags:
      - user
    put:
      consumes:
      - application/json
      description: Update the profile of the authenticated user
      parameters:
      - description: Profile update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/domain.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ProfileDTO'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/domain.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.APIResponse'
      security:
      - BearerAuth: []
      summary: Update user profile
      tags:
      - user
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
