{"swagger": "2.0", "info": {"description": "A production-grade API service for grocery e-commerce platform", "title": "B2C Core Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/auth/login": {"post": {"description": "Authenticate user and return tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Login user", "parameters": [{"description": "Login request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.LoginResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/auth/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Get current user's profile information", "produces": ["application/json"], "tags": ["auth"], "summary": "Get user profile", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.UserDTO"}}}]}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/auth/refresh": {"post": {"description": "Generate new access token using refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Refresh access token", "parameters": [{"description": "Refresh token request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.RefreshTokenRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.LoginResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/auth/register": {"post": {"description": "Create a new user account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Register a new user", "parameters": [{"description": "Registration request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.LoginResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/auth/verify": {"post": {"security": [{"BearerAuth": []}], "description": "Mark user account as verified", "produces": ["application/json"], "tags": ["auth"], "summary": "Verify user account", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/user/addresses": {"get": {"security": [{"BearerAuth": []}], "description": "Get all addresses of the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Get user addresses", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.AddressDTO"}}}}]}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new address for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Create user address", "parameters": [{"description": "Address creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateAddressRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.AddressDTO"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/user/addresses/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific address of the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Get user address", "parameters": [{"type": "string", "description": "Address ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.AddressDTO"}}}]}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a specific address of the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Update user address", "parameters": [{"type": "string", "description": "Address ID", "name": "id", "in": "path", "required": true}, {"description": "Address update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateAddressRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.AddressDTO"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a specific address of the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Delete user address", "parameters": [{"type": "string", "description": "Address ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/user/addresses/{id}/default": {"patch": {"security": [{"BearerAuth": []}], "description": "Set a specific address as the default address for the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Set default address", "parameters": [{"type": "string", "description": "Address ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}, "/user/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile of the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Get user profile", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ProfileDTO"}}}]}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update the profile of the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Update user profile", "parameters": [{"description": "Profile update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateProfileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ProfileDTO"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new profile for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Create user profile", "parameters": [{"description": "Profile creation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateProfileRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/domain.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ProfileDTO"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete the profile of the authenticated user", "produces": ["application/json"], "tags": ["user"], "summary": "Delete user profile", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/domain.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/domain.APIResponse"}}}}}}, "definitions": {"domain.APIResponse": {"type": "object", "properties": {"data": {}, "error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "domain.AddressDTO": {"type": "object", "properties": {"city": {"type": "string"}, "id": {"type": "string"}, "is_default": {"type": "boolean"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "location": {"type": "string"}, "pincode": {"type": "string"}, "state": {"type": "string"}, "type": {"type": "string"}, "user_id": {"type": "string"}}}, "domain.CreateAddressRequest": {"type": "object", "required": ["city", "line1", "pincode", "state", "type"], "properties": {"city": {"type": "string"}, "is_default": {"type": "boolean"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "location": {"type": "string"}, "pincode": {"type": "string"}, "state": {"type": "string"}, "type": {"type": "string"}}}, "domain.CreateProfileRequest": {"type": "object", "required": ["first_name", "last_name"], "properties": {"dob": {"type": "string"}, "first_name": {"type": "string"}, "gender": {"type": "string"}, "language": {"type": "string"}, "last_name": {"type": "string"}}}, "domain.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "domain.LoginResponse": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires_at": {"type": "string"}, "refresh_token": {"type": "string"}, "user": {"$ref": "#/definitions/domain.UserDTO"}}}, "domain.ProfileDTO": {"type": "object", "properties": {"dob": {"type": "string"}, "first_name": {"type": "string"}, "gender": {"type": "string"}, "id": {"type": "string"}, "language": {"type": "string"}, "last_name": {"type": "string"}, "user_id": {"type": "string"}}}, "domain.RefreshTokenRequest": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string"}}}, "domain.RegisterRequest": {"type": "object", "required": ["email", "password", "phone"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 6}, "phone": {"type": "string"}}}, "domain.UserDTO": {"type": "object", "properties": {"app_coins": {"type": "integer"}, "created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "is_verified": {"type": "boolean"}, "phone": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}