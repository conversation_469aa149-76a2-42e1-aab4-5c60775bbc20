-- Create profiles table
CREATE TABLE cashandcarry.profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES cashandcarry.users(id) ON DELETE CASCADE,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    gender VARCHAR(20),
    dob DATE,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_profiles_user_id ON cashandcarry.profiles(user_id);
CREATE INDEX idx_profiles_language ON cashandcarry.profiles(language);

-- Add trigger to automatically update updated_at
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON cashandcarry.profiles
    FOR EACH ROW
    EXECUTE FUNCTION cashandcarry.update_updated_at_column();

-- Add comments
COMMENT ON TABLE cashandcarry.profiles IS 'Extended user profile information';
COMMENT ON COLUMN cashandcarry.profiles.user_id IS 'Reference to the user account';
COMMENT ON COLUMN cashandcarry.profiles.first_name IS 'User first name';
COMMENT ON COLUMN cashandcarry.profiles.last_name IS 'User last name';
COMMENT ON COLUMN cashandcarry.profiles.gender IS 'User gender';
COMMENT ON COLUMN cashandcarry.profiles.dob IS 'Date of birth';
COMMENT ON COLUMN cashandcarry.profiles.language IS 'Preferred language (ISO 639-1 code)';
