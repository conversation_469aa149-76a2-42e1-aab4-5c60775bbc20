-- Create users table
CREATE TABLE cashandcarry.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password VARCHAR(255) NOT NULL,
    is_verified B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    app_coins BIGINT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON cashandcarry.users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_phone ON cashandcarry.users(phone) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_is_verified ON cashandcarry.users(is_verified) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_deleted_at ON cashandcarry.users(deleted_at);

-- Add trigger to automatically update updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON cashandcarry.users
    FOR EACH ROW
    EXECUTE FUNCTION cashandcarry.update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE cashandcarry.users IS 'User accounts for the B2C application';
COMMENT ON COLUMN cashandcarry.users.id IS 'Unique identifier for the user';
COMMENT ON COLUMN cashandcarry.users.email IS 'User email address (unique)';
COMMENT ON COLUMN cashandcarry.users.phone IS 'User phone number (unique)';
COMMENT ON COLUMN cashandcarry.users.password IS 'Hashed password';
COMMENT ON COLUMN cashandcarry.users.is_verified IS 'Whether the user account is verified';
COMMENT ON COLUMN cashandcarry.users.app_coins IS 'Virtual currency balance';
COMMENT ON COLUMN cashandcarry.users.created_at IS 'Timestamp when the user was created';
COMMENT ON COLUMN cashandcarry.users.updated_at IS 'Timestamp when the user was last updated';
COMMENT ON COLUMN cashandcarry.users.deleted_at IS 'Soft delete timestamp';
