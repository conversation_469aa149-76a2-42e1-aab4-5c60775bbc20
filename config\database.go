package config

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDatabase initializes the database connection and runs migrations
func InitDatabase(config *Config) (*gorm.DB, error) {
	var err error

	// Configure GORM logger
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	if config.IsProduction() {
		gormConfig.Logger = logger.Default.LogMode(logger.Error)
	}

	// Connect to database with retry logic
	maxRetries := 10
	retryDelay := 5 * time.Second

	log.Printf("Attempting to connect to database: %s", config.Database.Host)

	for i := 0; i < maxRetries; i++ {
		DB, err = gorm.Open(postgres.Open(config.Database.DSN), gormConfig)
		if err == nil {
			break
		}

		log.Printf("Database connection attempt %d/%d failed: %v", i+1, maxRetries, err)

		if i < maxRetries-1 {
			log.Printf("Retrying in %v...", retryDelay)
			time.Sleep(retryDelay)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database after %d attempts: %w", maxRetries, err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := DB.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")

	// Verify database schema exists (migrations should handle schema creation)
	if err := verifyDatabaseSetup(DB); err != nil {
		return nil, fmt.Errorf("database setup verification failed: %w", err)
	}

	return DB, nil
}

// verifyDatabaseSetup verifies that the database is properly configured
func verifyDatabaseSetup(db *gorm.DB) error {
	log.Println("Verifying database setup...")

	// Verify UUID extension is working (check in public schema)
	var testUUID string
	if err := db.Raw("SELECT public.uuid_generate_v4()").Scan(&testUUID).Error; err != nil {
		return fmt.Errorf("UUID extension verification failed: %w", err)
	}
	log.Printf("UUID extension verified with test UUID: %s", testUUID)

	// Check if cashandcarry schema exists
	var schemaExists bool
	if err := db.Raw("SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'cashandcarry')").Scan(&schemaExists).Error; err != nil {
		return fmt.Errorf("failed to check schema existence: %w", err)
	}

	if !schemaExists {
		log.Println("Schema 'cashandcarry' does not exist. Please run migrations: make migrate-up")
		return fmt.Errorf("schema 'cashandcarry' does not exist - run migrations first")
	}

	log.Println("Database setup verification completed successfully")
	return nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}

// CloseDB closes the database connection
func CloseDB() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Transaction helper function
func WithTransaction(fn func(*gorm.DB) error) error {
	tx := DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Health check function
func HealthCheck() error {
	if DB == nil {
		return fmt.Errorf("database connection is nil")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}
