-- Create shop_categories table
CREATE TABLE cashandcarry.shop_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    icon_url TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_shop_categories_name ON cashandcarry.shop_categories(name);

-- Add comments
COMMENT ON TABLE cashandcarry.shop_categories IS 'Categories for shops (e.g., Grocery, Pharmacy, Electronics)';
COMMENT ON COLUMN cashandcarry.shop_categories.name IS 'Category name';
COMMENT ON COLUMN cashandcarry.shop_categories.icon_url IS 'URL to category icon image';
