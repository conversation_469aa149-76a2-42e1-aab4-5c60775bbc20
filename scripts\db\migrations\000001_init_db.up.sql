-- Initialize database for B2C Core Service
-- This migration sets up the basic database structure

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the cashandcarry schema
CREATE SCHEMA IF NOT EXISTS cashandcarry;

-- Grant permissions to postgres user
GRANT ALL PRIVILEGES ON SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA cashandcarry TO postgres;

-- Set default search path for the database
ALTER DATABASE db_b2c SET search_path TO cashandcarry, public;

-- Create a function to update updated_at timestamp
CREATE OR REPLACE FUNCTION cashandcarry.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a function to set created_at and updated_at on insert
CREATE OR REPLACE FUNCTION cashandcarry.set_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    NEW.created_at = CURRENT_TIMESTAMP;
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';
