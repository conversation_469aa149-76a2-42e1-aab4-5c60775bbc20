-- Create addresses table
CREATE TABLE cashandcarry.addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES cashandcarry.users(id) ON DELETE CASCADE,
    line1 VARCHAR(255) NOT NULL,
    line2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    location TEXT, -- GPS coordinates or location description
    type VARCHAR(20) NOT NULL DEFAULT 'Home', -- Home, Work, Other
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_addresses_user_id ON cashandcarry.addresses(user_id);
CREATE INDEX idx_addresses_pincode ON cashandcarry.addresses(pincode);
CREATE INDEX idx_addresses_type ON cashandcarry.addresses(type);
CREATE INDEX idx_addresses_is_default ON cashandcarry.addresses(is_default) WHERE is_default = TRUE;

-- Add trigger to automatically update updated_at
CREATE TRIGGER update_addresses_updated_at
    BEFORE UPDATE ON cashandcarry.addresses
    FOR EACH ROW
    EXECUTE FUNCTION cashandcarry.update_updated_at_column();

-- Add constraint to ensure only one default address per user
CREATE UNIQUE INDEX idx_addresses_user_default 
    ON cashandcarry.addresses(user_id) 
    WHERE is_default = TRUE;

-- Add comments
COMMENT ON TABLE cashandcarry.addresses IS 'User delivery addresses';
COMMENT ON COLUMN cashandcarry.addresses.user_id IS 'Reference to the user account';
COMMENT ON COLUMN cashandcarry.addresses.line1 IS 'Address line 1';
COMMENT ON COLUMN cashandcarry.addresses.line2 IS 'Address line 2 (optional)';
COMMENT ON COLUMN cashandcarry.addresses.location IS 'GPS coordinates or location description';
COMMENT ON COLUMN cashandcarry.addresses.type IS 'Address type: Home, Work, Other';
COMMENT ON COLUMN cashandcarry.addresses.is_default IS 'Whether this is the default address for the user';
