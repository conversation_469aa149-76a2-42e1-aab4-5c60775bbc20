-- Basic database initialization for B2C Core Service
-- This script runs when the PostgreSQL container starts for the first time
-- For full schema setup, use the migration files in scripts/db/migrations/

\echo 'Starting basic database initialization...'

-- Enable UUID extension first (required for uuid_generate_v4())
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
\echo 'UUID extension enabled'

-- Verify UUID extension is working
SELECT uuid_generate_v4() AS test_uuid;
\echo 'UUID extension test completed'

\echo 'Basic database initialization completed!'
\echo 'Run migrations to create the full schema: make migrate-up'
