-- Initialize database for B2C Core Service
-- This script runs when the PostgreSQL container starts for the first time

\echo 'Starting database initialization...'

-- Enable UUID extension first (required for uuid_generate_v4())
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
\echo 'UUID extension enabled'

-- Create the cashandcarry schema
CREATE SCHEMA IF NOT EXISTS cashandcarry;
\echo 'Schema cashandcarry created'

-- Grant permissions to postgres user
GRANT ALL PRIVILEGES ON SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA cashandcarry TO postgres;
\echo 'Permissions granted'

-- Set default search path for the database
ALTER DATABASE db_b2c SET search_path TO cashandcarry, public;
\echo 'Search path configured'

-- Verify UUID extension is working
SELECT uuid_generate_v4() AS test_uuid;
\echo 'UUID extension test completed'

\echo 'Database initialization completed successfully!'
