package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"b2c-core-service/config"
	_ "b2c-core-service/docs"
	"b2c-core-service/internal/auth"
	commonAuth "b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/middlewares"
	"b2c-core-service/internal/user"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title B2C Core Service API
// @version 1.0
// @description A production-grade API service for grocery e-commerce platform
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize database
	db, err := config.InitDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer config.CloseDB()

	// Initialize JWT service
	jwtService := commonAuth.NewJWTService(cfg)

	// Initialize repositories
	authRepo := auth.NewRepository(db)
	userRepo := user.NewRepository(db)

	// Initialize services
	authService := auth.NewService(authRepo, jwtService)
	userService := user.NewService(userRepo)

	// Initialize handlers
	authHandler := auth.NewHandler(authService)
	userHandler := user.NewHandler(userService)

	// Setup Gin router
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Global middlewares
	router.Use(middlewares.LoggingMiddleware())
	router.Use(middlewares.RequestIDMiddleware())
	router.Use(middlewares.CORSMiddleware())
	router.Use(middlewares.ErrorMiddleware())
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		// Check database health
		if err := config.HealthCheck(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status":    "unhealthy",
				"database":  "down",
				"timestamp": time.Now().UTC(),
				"error":     err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"database":  "up",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
	})

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Setup auth routes
		auth.SetupRoutes(v1, authHandler, jwtService)

		// Setup user routes
		user.SetupRoutes(v1, userHandler, jwtService)

		// Placeholder for other modules
		// product.SetupRoutes(v1, productHandler, jwtService)
		// cart.SetupRoutes(v1, cartHandler, jwtService)
		// order.SetupRoutes(v1, orderHandler, jwtService)
	}

	// Swagger documentation
	if cfg.IsDevelopment() {
		router.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		log.Println("Swagger UI available at: http://localhost:" + cfg.Server.Port + "/docs/index.html")
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Server.Port,
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on port %s", cfg.Server.Port)
		log.Printf("Environment: %s", cfg.Server.Environment)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
